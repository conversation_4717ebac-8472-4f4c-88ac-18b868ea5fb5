import config from "@src/config.json";
import { useMutation } from "@tanstack/react-query";
import type { ChatRequest, ChatResponse, QueryResult } from "../Api.types";

type Props = ChatRequest;
type Result = QueryResult<ChatResponse>;

const send = async (props: Props): Promise<Result> => {
	let result: Result;

	try {
		const response = await fetch(`http://localhost:${config.serverAi.port}/chat`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				message: props.message,
				conversationId: props.conversationId || "default",
			}),
		});

		const res = await response.json();

		if (response.ok) {
			result = {
				error: 0,
				message: "",
				response: res,
			};
		} else {
			result = {
				error: 2,
				message: res.error || "Failed to send message",
			};
		}
	} catch (_error) {
		result = {
			error: 1,
			message: "Network error",
		};
	}

	return result;
};

export const useMutateChat = () => {
	return useMutation({
		mutationFn: send,
	});
};
